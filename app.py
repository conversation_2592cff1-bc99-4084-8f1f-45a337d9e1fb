import streamlit as st
import pandas as pd
import datetime
from kite_api import get_historical_data
from pattern_engine import detect_patterns
from utils.indicators import calculate_indicators
from utils.chart_utils import plot_candles_with_patterns
from ml_models.predictor import predict_ce_pe

st.set_page_config(layout="wide")

st.title("📈 Live Options ML Prediction & Pattern Detector")

# Select stock/index
symbol = st.selectbox("Select Instrument", ["NIFTY", "BANKNIFTY", "FINNIFTY"])

# Select timeframe
timeframe = st.selectbox("Select Timeframe", ["1m", "5m", "15m"])

# Date range
start_date = st.date_input("From", datetime.date.today() - datetime.timedelta(days=5))
end_date = st.date_input("To", datetime.date.today())

# Submit
if st.button("📊 Analyze Now"):
    with st.spinner("Fetching & analyzing data..."):
        try:
            df = get_historical_data(symbol, interval=timeframe, from_date=start_date, to_date=end_date)
            st.write("Fetched DataFrame:", df)
        except Exception as e:
            st.error(f"Error fetching data: {e}")
            st.stop()

        if df is None or df.empty:
            st.error("Failed to fetch data from Kite or data is empty.")
            st.stop()
        else:
            try:
                # Calculate indicators
                df = calculate_indicators(df)
                st.write("After indicators:", df.head())
            except Exception as e:
                st.error(f"Error calculating indicators: {e}")
                st.stop()

            try:
                # Detect patterns
                pattern_results = detect_patterns(df)
                st.write("Pattern results:", pattern_results)
            except Exception as e:
                st.error(f"Error detecting patterns: {e}")
                st.stop()

            try:
                # Predict CE/PE signal
                cepe_result = predict_ce_pe(df)
                st.write("ML Prediction:", cepe_result)
            except Exception as e:
                st.error(f"Error in ML prediction: {e}")
                st.stop()

            # Show signal
            st.subheader("📌 ML Signal:")
            st.success(f"**Suggested Option:** {cepe_result['signal']} | Confidence: {cepe_result['confidence']}%")
            st.info(f"SL: {cepe_result['stoploss']} | Target: {cepe_result['target']}")

            # Plot chart
            try:
                st.subheader("📈 Candlestick Chart with Patterns")
                fig = plot_candles_with_patterns(df, pattern_results)
                st.pyplot(fig)
            except Exception as e:
                st.error(f"Error plotting chart: {e}")

import json
import threading
import time
import pandas as pd
import requests
from datetime import datetime, timedelta
import logging
import os
from market_status import should_use_live_data, get_market_status_display

# Try to import kiteconnect for proper WebSocket
try:
    from kiteconnect import KiteTicker
    KITE_CONNECT_AVAILABLE = True
except ImportError:
    KITE_CONNECT_AVAILABLE = False
    print("Warning: kiteconnect not available. Install with: pip install kiteconnect")

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class KiteTickerEngine:
    def __init__(self, api_key=None, access_token=None, enctoken_path=None):
        self.api_key = api_key
        self.access_token = access_token
        self.enctoken = None
        self.kws = None
        self.connected = False
        self.subscribed_tokens = []
        self.tick_data = {}  # Store latest tick data
        self.candles = {}    # Store candle data for each instrument
        self.last_candle_time = {}  # Track last candle timestamp

        # Load authentication
        self._load_authentication(enctoken_path)

        # Initialize WebSocket based on available authentication
        if KITE_CONNECT_AVAILABLE and self.api_key and self.access_token:
            self._init_kite_ticker()
        else:
            logger.warning("KiteTicker not available or credentials missing. Using demo mode.")
            self.kws = None

    def _load_authentication(self, enctoken_path):
        """Load authentication credentials from various sources"""

        # Try to load from access_token.json first (preferred method)
        if os.path.exists("access_token.json"):
            try:
                with open("access_token.json", "r") as f:
                    credentials = json.load(f)
                self.api_key = credentials.get("api_key")
                self.access_token = credentials.get("access_token")
                logger.info("Credentials loaded from access_token.json")
                return
            except Exception as e:
                logger.warning(f"Could not load credentials from access_token.json: {e}")

        # Fallback to enctoken if provided
        if enctoken_path and os.path.exists(enctoken_path):
            try:
                with open(enctoken_path, 'r') as f:
                    self.enctoken = f.read().strip()
                logger.info("Enctoken loaded from file")
            except FileNotFoundError:
                logger.error(f"Enctoken file not found: {enctoken_path}")

        # If no credentials found, log warning
        if not self.api_key and not self.enctoken:
            logger.warning("No authentication credentials found. Please authenticate first.")

    def _init_kite_ticker(self):
        """Initialize official Kite Ticker"""
        try:
            self.kws = KiteTicker(self.api_key, self.access_token)

            # Set up callbacks
            self.kws.on_ticks = self._on_ticks
            self.kws.on_connect = self._on_connect
            self.kws.on_close = self._on_close
            self.kws.on_error = self._on_error
            self.kws.on_reconnect = self._on_reconnect
            self.kws.on_noreconnect = self._on_noreconnect

            logger.info("KiteTicker initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize KiteTicker: {e}")
            self.kws = None
    
    # KiteTicker callback methods
    def _on_ticks(self, ws, ticks):
        """Callback for receiving tick data"""
        try:
            for tick in ticks:
                self._process_tick(tick)
        except Exception as e:
            logger.error(f"Error processing ticks: {e}")

    def _on_connect(self, ws, response):
        """Callback for successful connection"""
        self.connected = True
        logger.info("Successfully connected to KiteTicker WebSocket")

        # Resubscribe to tokens if any
        if self.subscribed_tokens:
            logger.info(f"Resubscribing to {len(self.subscribed_tokens)} tokens")
            self.kws.subscribe(self.subscribed_tokens)
            self.kws.set_mode(self.kws.MODE_FULL, self.subscribed_tokens)

    def _on_close(self, ws, code, reason):
        """Callback for connection close"""
        self.connected = False
        logger.warning(f"WebSocket connection closed: {code} - {reason}")

    def _on_error(self, ws, code, reason):
        """Callback for connection error"""
        self.connected = False
        logger.error(f"WebSocket error: {code} - {reason}")

    def _on_reconnect(self, ws, attempts_count):
        """Callback for reconnection attempt"""
        logger.info(f"Reconnecting... attempt {attempts_count}")

    def _on_noreconnect(self, ws):
        """Callback when max reconnection attempts reached"""
        logger.error("Max reconnection attempts reached. Connection failed.")
    
    def _process_tick(self, tick):
        """Process incoming tick data"""
        try:
            instrument_token = tick['instrument_token']

            # Store the latest tick
            self.tick_data[instrument_token] = tick

            # Update candle data
            self._update_candle(instrument_token, tick)

        except Exception as e:
            logger.error(f"Error processing individual tick: {e}")
            logger.debug(f"Tick data: {tick}")
    
    def _update_candle(self, token, tick):
        """Convert tick data to candle data"""
        # Use exchange_timestamp if available, otherwise use current time
        if 'exchange_timestamp' in tick and tick['exchange_timestamp']:
            current_time = tick['exchange_timestamp']
        elif 'timestamp' in tick:
            current_time = datetime.fromtimestamp(tick['timestamp'])
        else:
            current_time = datetime.now()

        # Round down to the nearest minute for 1-min candles
        candle_time = current_time.replace(second=0, microsecond=0)
        
        # Initialize candle storage for this token if not exists
        if token not in self.candles:
            self.candles[token] = pd.DataFrame(columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            self.last_candle_time[token] = None
        
        # If this is a new candle
        if self.last_candle_time.get(token) != candle_time:
            # Add new candle
            new_candle = {
                'timestamp': candle_time,
                'open': tick['last_price'],
                'high': tick['last_price'],
                'low': tick['last_price'],
                'close': tick['last_price'],
                'volume': tick.get('volume', 0)
            }
            
            # Fix pandas concatenation warning
            new_candle_df = pd.DataFrame([new_candle])
            if not self.candles[token].empty:
                self.candles[token] = pd.concat([
                    self.candles[token],
                    new_candle_df
                ], ignore_index=True)
            else:
                self.candles[token] = new_candle_df
            
            self.last_candle_time[token] = candle_time
        else:
            # Update existing candle
            idx = self.candles[token].index[-1]
            self.candles[token].at[idx, 'high'] = max(self.candles[token].at[idx, 'high'], tick['last_price'])
            self.candles[token].at[idx, 'low'] = min(self.candles[token].at[idx, 'low'], tick['last_price'])
            self.candles[token].at[idx, 'close'] = tick['last_price']
            self.candles[token].at[idx, 'volume'] = tick.get('volume', 0)
    
    def _on_error(self, ws, error):
        """Handle WebSocket errors"""
        logger.error(f"WebSocket Error: {error}")
        self.connected = False

    def _on_close(self, ws, close_status_code, close_msg):
        """Handle WebSocket connection close"""
        self.connected = False
        logger.warning(f"WebSocket connection closed: {close_status_code} - {close_msg}")

        # Attempt reconnection if not intentional
        if self.reconnect_attempts < self.max_reconnect_attempts:
            self.reconnect_attempts += 1
            logger.info(f"Attempting reconnection {self.reconnect_attempts}/{self.max_reconnect_attempts}")
            time.sleep(5)  # Wait before reconnecting
            try:
                self.connect()
            except Exception as e:
                logger.error(f"Reconnection failed: {e}")

    def _on_open(self, ws):
        """Handle WebSocket connection open"""
        self.connected = True
        self.reconnect_attempts = 0  # Reset reconnection counter
        logger.info("WebSocket connection established")

        # Subscribe to tokens
        if self.subscribed_tokens:
            self._subscribe(self.subscribed_tokens)
    
    def connect(self):
        """Connect to WebSocket with market status awareness"""

        # Check market status first
        market_status = get_market_status_display()
        logger.info(f"Market Status: {market_status['text']}")

        if not should_use_live_data():
            logger.warning("Market is closed. Live data may not be accurate.")
            logger.info("Consider using demo mode for testing during market hours.")

        if not self.kws:
            logger.error("KiteTicker not initialized. Cannot connect.")
            return False

        try:
            # Connect using KiteTicker (runs in threaded mode by default)
            self.kws.connect(threaded=True)

            # Wait a moment for connection to establish
            time.sleep(3)

            return self.connected
        except Exception as e:
            logger.error(f"Failed to connect to KiteTicker: {e}")
            return False


    
    def subscribe(self, instrument_tokens):
        """Subscribe to instrument tokens"""
        if not self.kws:
            logger.error("KiteTicker not initialized. Cannot subscribe.")
            return False

        try:
            self.subscribed_tokens = instrument_tokens

            if self.connected:
                # Subscribe to tokens
                self.kws.subscribe(instrument_tokens)
                # Set mode to full for detailed data
                self.kws.set_mode(self.kws.MODE_FULL, instrument_tokens)
                logger.info(f"Subscribed to {len(instrument_tokens)} instruments")
                return True
            else:
                logger.warning("Not connected. Tokens will be subscribed when connection is established.")
                return False

        except Exception as e:
            logger.error(f"Failed to subscribe: {e}")
            return False
    
    def get_latest_tick(self, instrument_token):
        """Get the latest tick for an instrument"""
        return self.tick_data.get(instrument_token)
    
    def get_candles(self, instrument_token, lookback=100):
        """Get candle data for an instrument"""
        if instrument_token not in self.candles:
            return pd.DataFrame()
        
        df = self.candles[instrument_token].copy()
        if len(df) > lookback:
            df = df.tail(lookback)
        
        return df
    
    def disconnect(self):
        """Close WebSocket connection"""
        if self.kws:
            try:
                self.kws.close()
                logger.info("WebSocket connection closed")
            except Exception as e:
                logger.error(f"Error closing WebSocket: {e}")
        self.connected = False

# Helper function to get instrument token from symbol
def get_instrument_token(symbol):
    """Get instrument token for a symbol using the Kite API"""
    # Hardcoded instrument tokens for major indices (more reliable)
    instrument_tokens = {
        "NIFTY": 256265,      # NIFTY 50
        "BANKNIFTY": 260105,  # NIFTY BANK
        "FINNIFTY": 257801    # NIFTY FIN SERVICE
    }

    if symbol in instrument_tokens:
        return instrument_tokens[symbol]

    # Fallback: Try to get from Kite API
    try:
        from kite_auth_helper import get_kite_connection
        kite = get_kite_connection()

        if kite:
            instrument_map = {
                "NIFTY": "NSE:NIFTY 50",
                "BANKNIFTY": "NSE:NIFTY BANK",
                "FINNIFTY": "NSE:NIFTY FIN SERVICE"
            }

            if symbol in instrument_map:
                quote = kite.ltp(instrument_map[symbol])
                return list(quote.keys())[0]  # Extract instrument token from response

        raise ValueError(f"Could not get instrument token for {symbol}")

    except Exception as e:
        logger.error(f"Error getting instrument token: {e}")
        # Return hardcoded token as fallback
        if symbol in instrument_tokens:
            return instrument_tokens[symbol]
        else:
            raise ValueError(f"Unknown symbol: {symbol}")

# Test function for WebSocket engine
def test_websocket():
    """Test the WebSocket connection"""
    try:
        # Try to load credentials from access_token.json first
        engine = KiteTickerEngine()

        if not engine.connect():
            logger.error("Failed to connect to WebSocket")
            return

        # Test with NIFTY
        symbol = "NIFTY"
        instrument_token = get_instrument_token(symbol)
        engine.subscribe([instrument_token])

        logger.info(f"Connected and subscribed to {symbol}")

        # Keep running for testing
        while True:
            candles = engine.get_candles(instrument_token)
            if not candles.empty:
                logger.info(f"Received {len(candles)} candles for {symbol}")
                logger.info(f"Latest price: {candles['close'].iloc[-1]}")
            time.sleep(5)

    except KeyboardInterrupt:
        logger.info("Test stopped by user")
    except Exception as e:
        logger.error(f"Test failed: {e}")

if __name__ == "__main__":
    test_websocket()

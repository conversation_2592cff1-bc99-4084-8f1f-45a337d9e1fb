import os
import numpy as np
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import Conv2D, MaxPooling2D, Flatten, Dense
from tensorflow.keras.preprocessing.image import ImageDataGenerator

# Assumes you have folders like: patterns/bullish_engulfing/, patterns/head_and_shoulders/, etc.
datagen = ImageDataGenerator(rescale=1./255, validation_split=0.2)

train_data = datagen.flow_from_directory(
    'patterns/',
    target_size=(64, 64),
    class_mode='categorical',
    subset='training'
)

val_data = datagen.flow_from_directory(
    'patterns/',
    target_size=(64, 64),
    class_mode='categorical',
    subset='validation'
)

model = Sequential([
    Conv2D(32, (3, 3), activation='relu', input_shape=(64, 64, 3)),
    MaxPooling2D(),
    Conv2D(64, (3, 3), activation='relu'),
    MaxPooling2D(),
    <PERSON>ten(),
    <PERSON><PERSON>(64, activation='relu'),
    <PERSON><PERSON>(train_data.num_classes, activation='softmax')
])

model.compile(optimizer='adam', loss='categorical_crossentropy', metrics=['accuracy'])
model.fit(train_data, epochs=10, validation_data=val_data)

model.save("ml_models/pattern_cnn.h5")
print("✅ CNN pattern model saved.")

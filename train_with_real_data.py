#!/usr/bin/env python3
"""
Train ML Model with Real 90-Day Historical Data from Kite API
This script fetches real market data and trains a proper ML model
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.model_selection import TimeSeriesSplit, cross_val_score
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
from sklearn.preprocessing import StandardScaler
import joblib
import warnings
warnings.filterwarnings('ignore')

# Import our modules
from kite_auth_helper import get_kite_connection
from utils.indicators import calculate_indicators
from pattern_engine import detect_patterns, calculate_support_resistance

def fetch_historical_data_90_days(symbol="NIFTY", interval="5minute"):
    """Fetch 90 days of historical data from Kite API"""
    print(f"📈 Fetching 90 days of {symbol} data ({interval})...")
    
    try:
        kite = get_kite_connection()
        if not kite:
            raise Exception("Failed to connect to Kite API")
        
        # Calculate date range (90 days back)
        end_date = datetime.now()
        start_date = end_date - timedelta(days=90)
        
        # Get instrument token
        instruments = kite.instruments("NSE")
        instrument_map = {
            "NIFTY": "NIFTY 50",
            "BANKNIFTY": "NIFTY BANK", 
            "FINNIFTY": "NIFTY FIN SERVICE"
        }
        
        instrument_token = None
        for inst in instruments:
            if inst['name'] == instrument_map.get(symbol, symbol):
                instrument_token = inst['instrument_token']
                break
        
        if not instrument_token:
            raise Exception(f"Instrument token not found for {symbol}")
        
        print(f"🔍 Found instrument token: {instrument_token}")
        
        # Fetch historical data
        historical_data = kite.historical_data(
            instrument_token=instrument_token,
            from_date=start_date,
            to_date=end_date,
            interval=interval
        )
        
        # Convert to DataFrame
        df = pd.DataFrame(historical_data)
        df['timestamp'] = df['date']
        df = df.drop('date', axis=1)
        
        print(f"✅ Fetched {len(df)} candles from {start_date.date()} to {end_date.date()}")
        return df
        
    except Exception as e:
        print(f"❌ Error fetching data: {e}")
        print("📁 Falling back to existing data...")
        
        # Fallback to existing data
        try:
            df = pd.read_csv("data/fallback.csv")
            print(f"📁 Loaded fallback data: {len(df)} rows")
            return df
        except:
            print("❌ No fallback data available!")
            return None

def create_trading_targets(df):
    """Create realistic trading targets based on actual price movements"""
    print("🎯 Creating trading targets...")

    # Ensure we have a proper close column
    if 'close' not in df.columns:
        print("❌ No 'close' column found!")
        return df

    # Calculate future returns (next 5 candles) - more robust
    df = df.copy()
    df['future_return_5'] = df['close'].shift(-5) / df['close'] - 1

    # Create targets based on significant moves
    threshold = 0.003  # 0.3% move threshold (more realistic for 5min data)

    # Initialize target column
    df['target'] = 1  # Default neutral

    # Only create targets where we have valid future returns
    valid_returns = df['future_return_5'].notna()

    # CE target: Strong upward move expected
    ce_condition = (df['future_return_5'] > threshold) & valid_returns
    df.loc[ce_condition, 'target'] = 2

    # PE target: Strong downward move expected
    pe_condition = (df['future_return_5'] < -threshold) & valid_returns
    df.loc[pe_condition, 'target'] = 0

    # Remove rows where we can't calculate future returns (last 5 rows)
    df = df[:-5].copy()

    # Target distribution
    target_dist = df['target'].value_counts().sort_index()
    print(f"📊 Target Distribution:")
    print(f"   PE (0): {target_dist.get(0, 0)} samples")
    print(f"   Neutral (1): {target_dist.get(1, 0)} samples")
    print(f"   CE (2): {target_dist.get(2, 0)} samples")

    # Check if we have reasonable distribution
    total_signals = target_dist.get(0, 0) + target_dist.get(2, 0)
    total_samples = len(df)
    signal_ratio = total_signals / total_samples if total_samples > 0 else 0

    print(f"📈 Signal ratio: {signal_ratio:.1%} (should be 10-30%)")

    return df

def engineer_features(df):
    """Create advanced features for ML model"""
    print("🔧 Engineering features...")

    # Ensure we have required base columns
    required_base = ['RSI', 'MACD', 'VWAP', 'close', 'high', 'low', 'volume']
    missing_base = [col for col in required_base if col not in df.columns]
    if missing_base:
        print(f"❌ Missing base columns: {missing_base}")
        return df

    # Technical indicator features (safe operations)
    df['rsi_oversold'] = (df['RSI'] < 30).astype(int)
    df['rsi_overbought'] = (df['RSI'] > 70).astype(int)
    df['rsi_momentum'] = df['RSI'].diff()

    # MACD features
    df['macd_signal'] = (df['MACD'] > 0).astype(int)
    df['macd_momentum'] = df['MACD'].diff()

    # Price vs VWAP (handle division by zero)
    df['price_vs_vwap'] = (df['close'] > df['VWAP']).astype(int)
    df['vwap_distance'] = np.where(df['VWAP'] != 0,
                                   (df['close'] - df['VWAP']) / df['VWAP'],
                                   0)

    # Volatility features (handle division by zero)
    df['price_range'] = np.where(df['close'] != 0,
                                 (df['high'] - df['low']) / df['close'],
                                 0)

    # Volume ratio (handle rolling mean)
    volume_ma = df['volume'].rolling(20, min_periods=1).mean()
    df['volume_ratio'] = np.where(volume_ma != 0,
                                  df['volume'] / volume_ma,
                                  1.0)

    # Trend features (use min_periods for rolling)
    df['sma_5'] = df['close'].rolling(5, min_periods=1).mean()
    df['sma_20'] = df['close'].rolling(20, min_periods=1).mean()
    df['trend_short'] = (df['close'] > df['sma_5']).astype(int)
    df['trend_long'] = (df['close'] > df['sma_20']).astype(int)

    print(f"✅ Features engineered. Data shape: {df.shape}")

    return df

def train_ml_model(df):
    """Train ML model with proper validation"""
    print("🤖 Training ML model...")

    # Feature columns
    feature_cols = [
        'RSI', 'MACD', 'VWAP',
        'rsi_oversold', 'rsi_overbought', 'rsi_momentum',
        'macd_signal', 'macd_momentum',
        'price_vs_vwap', 'vwap_distance',
        'price_range', 'volume_ratio',
        'trend_short', 'trend_long'
    ]

    # Debug: Check data before cleaning
    print(f"🔍 Data before cleaning: {len(df)} rows")
    print(f"🔍 Available columns: {list(df.columns)}")

    # Check for missing columns
    missing_cols = [col for col in feature_cols + ['target'] if col not in df.columns]
    if missing_cols:
        print(f"❌ Missing columns: {missing_cols}")
        return None, None, 0

    # Check NaN values per column
    nan_counts = df[feature_cols + ['target']].isnull().sum()
    print(f"🔍 NaN counts per column:")
    for col, count in nan_counts.items():
        if count > 0:
            print(f"   {col}: {count} NaN values")

    # Prepare data with better NaN handling
    df_subset = df[feature_cols + ['target']].copy()

    # Fill NaN values intelligently instead of dropping
    for col in feature_cols:
        if col in ['rsi_momentum', 'macd_momentum']:
            df_subset[col] = df_subset[col].fillna(0)  # Momentum can be 0
        elif col == 'volume_ratio':
            df_subset[col] = df_subset[col].fillna(1.0)  # Default ratio is 1
        else:
            df_subset[col] = df_subset[col].fillna(df_subset[col].median())

    # Now drop only rows where target is NaN
    df_clean = df_subset.dropna(subset=['target'])

    print(f"🔍 Data after cleaning: {len(df_clean)} rows")

    if len(df_clean) < 100:
        print(f"❌ Insufficient data: {len(df_clean)} samples")
        print(f"💡 Try reducing the lookback period or using different features")
        return None, None, 0
    
    X = df_clean[feature_cols]
    y = df_clean['target']
    
    print(f"📊 Training data: {len(X)} samples, {len(feature_cols)} features")
    
    # Scale features
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)
    
    # Time series cross-validation
    tscv = TimeSeriesSplit(n_splits=5)
    
    # Train model
    model = RandomForestClassifier(
        n_estimators=200,
        max_depth=15,
        min_samples_split=20,
        min_samples_leaf=10,
        random_state=42,
        class_weight='balanced',
        n_jobs=-1
    )
    
    # Cross-validation
    cv_scores = cross_val_score(model, X_scaled, y, cv=tscv, scoring='accuracy')
    
    print(f"📈 Cross-validation scores: {cv_scores}")
    print(f"🎯 Mean CV Accuracy: {cv_scores.mean():.2%} ± {cv_scores.std():.2%}")
    
    # Train final model
    model.fit(X_scaled, y)
    
    # Feature importance
    importance_df = pd.DataFrame({
        'feature': feature_cols,
        'importance': model.feature_importances_
    }).sort_values('importance', ascending=False)
    
    print(f"\n🔍 Top 5 Important Features:")
    for _, row in importance_df.head().iterrows():
        print(f"   {row['feature']}: {row['importance']:.3f}")
    
    return model, scaler, cv_scores.mean()

def save_model_and_data(model, scaler, accuracy, df):
    """Save trained model and training data"""
    print("💾 Saving model and data...")
    
    # Save model
    joblib.dump(model, "ml_models/real_data_classifier.pkl")
    joblib.dump(scaler, "ml_models/real_data_scaler.pkl")
    
    # Save training data for future reference
    df.to_csv("data/training_data_90days.csv", index=False)
    
    # Save model info
    model_info = {
        'training_date': datetime.now().isoformat(),
        'accuracy': accuracy,
        'samples': len(df),
        'features': 14,
        'validation': 'TimeSeriesSplit',
        'data_source': 'Kite API 90 days'
    }
    
    import json
    with open("ml_models/real_data_model_info.json", "w") as f:
        json.dump(model_info, f, indent=2)
    
    print(f"✅ Model saved with {accuracy:.2%} accuracy")

def main():
    """Main training pipeline"""
    print("🚀 TRAINING ML MODEL WITH REAL 90-DAY DATA")
    print("=" * 60)
    
    # Step 1: Fetch historical data
    df = fetch_historical_data_90_days("NIFTY", "5minute")
    
    if df is None or len(df) < 100:
        print("❌ Insufficient data for training!")
        return
    
    # Step 2: Calculate indicators
    print("📊 Calculating technical indicators...")
    df = calculate_indicators(df)
    
    # Step 3: Create targets
    df = create_trading_targets(df)
    
    # Step 4: Engineer features
    df = engineer_features(df)
    
    # Step 5: Train model
    model, scaler, accuracy = train_ml_model(df)
    
    if model is None:
        print("❌ Model training failed!")
        return
    
    # Step 6: Save everything
    save_model_and_data(model, scaler, accuracy, df)
    
    print(f"\n🎉 SUCCESS! Real data model trained with {accuracy:.2%} accuracy")
    print(f"📁 Files saved:")
    print(f"   • ml_models/real_data_classifier.pkl")
    print(f"   • ml_models/real_data_scaler.pkl") 
    print(f"   • data/training_data_90days.csv")
    print(f"   • ml_models/real_data_model_info.json")

if __name__ == "__main__":
    main()
